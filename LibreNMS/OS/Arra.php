<?php

namespace <PERSON>bre<PERSON>MS\OS;

use App\Models\Device;
use App\Models\Plugin;
use Exception;
use LibreNMS\Device\WirelessSensor;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessChannelDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessClientsDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessClientsRateDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessFrequencyDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessNodeinfoDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessNoiseFloorDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessPowerDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessRateDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessSnrDiscovery;
use LibreNMS\Interfaces\Discovery\Sensors\WirelessWidthDiscovery;
use LibreNMS\OS;


class Arra extends OS implements
    WirelessClientsDiscovery,
    WirelessFrequencyDiscovery,
    WirelessChannelDiscovery,
    WirelessPowerDiscovery,
    WirelessWidthDiscovery,
    WirelessNodeinfoDiscovery,
    WirelessClientsRateDiscovery,
    WirelessNoiseFloorDiscovery,
    WirelessRateDiscovery,
    WirelessSnrDiscovery
{
    /**
     * Retrieve (and explode to array) list of network interfaces, and desired display name in LibreNMS.
     * This information is returned from the wireless device (router / AP) - as SNMP extend, with the name "interfaces".
     *
     * @return array Interfaces
     * @throws Exception
     */
    private function getInterfaces(): array
    {
        // Need to use PHP_EOL, found newline (\n) not near as reliable / consistent! And this is as PHP says it should be done.
        $interfaces = explode(PHP_EOL, snmp_get($this->getDevice()->toArray(), 'NET-SNMP-EXTEND-MIB::nsExtendOutputFull."interfaces"', '-Osqnv'));
        $arrIfaces = array();
        foreach ($interfaces as $interface) {
                list($k, $v) = explode(',', $interface);
                $arrIfaces[$k] = $v;
        }
        return $arrIfaces;
    }

    private function checkArraCPHash(): void
    {
        $plugin = Plugin::where('plugin_name', 'arra-captive-portal-management')->where('plugin_active', 1)->first();
        if (!$plugin) {
            return;
        }
    	$deviceId = $this->getDeviceId();
    	$device = Device::with('groups')->find($deviceId);
    	if (!empty($device)) {
    		$group = $device->groups->first();
    		if (!empty($group)) {
    			$deviceHash = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-captive-portal-hash"', '-Osqnv');
    			if (!empty($deviceHash)) {
                    $zipFileUrl = \Arra\CaptivePortalsManagement\Repositories\ArraCPMRepository::getCPDownloadUrl($group->id);
    				if ($zipFileUrl) {
    				    $existingHash = hash_file('sha256', $zipFileUrl);
    				    // echo "Current Hash File is: \n".$existingHash;
    				    // echo "\narra-aptive-portal-hash for deviceID {$deviceId}-{$group->id} is: ",$deviceHash;
    				    // die();
    				    if ($existingHash != $deviceHash) {
                            \Arra\CaptivePortalsManagement\Jobs\UpdateCaptivePortalJob::dispatch($deviceId)->onQueue('device_actions');
    				    }
    				}
    			}
    		}
    	}
    }

    private function checkArraSettingsHash(): void
    {
        $plugin = Plugin::where('plugin_name', 'arra-group-settings')->where('plugin_active', 1)->first();
        if (!$plugin) {
            return;
        }
    	$deviceId = $this->getDeviceId();
    	$device = Device::with('groups')->find($deviceId);
    	if (!empty($device)) {
    		$group = $device->groups->first();
    		if (!empty($group)) {
    			$deviceHash = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-settings-hash"', '-Osqnv');
    			if (!empty($deviceHash)) {
    				$rev_file = snmp_get($this->getDevice(), 'NET-SNMP-EXTEND-MIB::nsExtendOutput1Line."arra-settings-rev"', '-Osqnv');
                    if (empty($rev_file)) {
                        $rev_file = 1;
                    } else {
                        $rev_file = 0;
                    }
    				$configFileContent = \Arra\GroupSettings\Repositories\ArraGroupSettingsRepository::getGroupConfigFileContent($group->id, $rev_file);
    				if (!empty($configFileContent)) {
    				    $currentHash = hash('sha256', $configFileContent);
    				    // echo "Current Hash File is: \n".$currentHash;
    				    // echo "\narra-settings-hash for deviceID {$deviceId}-{$group->id} is: ",$deviceHash;
    				    // die();
    				    if ($currentHash != $deviceHash) {
                            \Arra\GroupSettings\Jobs\UpdateSettingsJob::dispatch($deviceId)->onQueue('device_actions');
    				    }
    				}
    			}
    		}
    	}
    }

    /**
     * Generic (common / shared) routine, to create new Wireless Sensors, of the sensor Type passed as the call argument.
     * type - string, matching to LibreNMS documentation => https://docs.librenms.org/Developing/os/Wireless-Sensors/
     * query - string, query to be used at client (appends to type string, e.g. -tx, -rx)
     * system - boolean, flag to indicate that a combined ("system level") sensor (and OID) is to be added
     * stats - boolean, flag denoting that statistics are to be retrieved (min, max, avg)
     * NOTE: system and stats are assumed to be mutually exclusive (at least for now!)
     *
     * @return array Sensors
     * @throws Exception
     */
    private function getSensorData($type, $query = '', $system = False, $stats = False): array
    {
	    // Initialize needed variables, and get interfaces (actual network name, and LibreNMS name)
        $sensors = [];
        $this->checkArraCPHash();
        $this->checkArraSettingsHash();
        $interfaces = $this->getInterfaces();

		$count = 1;

		// Build array for stats - if desired
		$statstr = [''];
		if ($stats) {
			$statstr = ['-min', '-max', '-avg'];
		}

		// Loop over interfaces, adding sensors
		foreach ($interfaces as $index => $interface) {
			// Loop over stats, appending to sensors as needed (only a single, blank, addition if no stats)
			foreach ($statstr as $stat) {
		                $oid = "NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"$type$query-$index$stat\"";
	        	        $sensors[] = new WirelessSensor($type, $this->getDeviceId(), snmp_translate($oid), "arra$query", $count, "$interface$query$stat");
	                	$count += 1;
			}
        }
	    // If system level (i.e. overall) sensor desired, add that one as well
        if ($system and (count($interfaces) > 1)) {
                $oid = "NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"$type$query-wlan\"";
                $sensors[] = new WirelessSensor($type, $this->getDeviceId(), snmp_translate($oid), "arra$query", $count, 'wlan');
        }

	    // And, return all the sensors that have been created above (i.e. the array of sensors)
        return $sensors;
    }

    /**
     * Discover wireless client counts. Type is clients.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessClients(): array
    {
        //return $this->getSensorData('clients', '', True, False);

		$interfaces = $this->getInterfaces();

		$sensors = array();

		foreach ($interfaces as $interface => $interfaceName) {

            $wlClients = $this->getWlClients($interface);
			$arrWlClients = array();
			$count = 0;
			foreach ($wlClients as $wlClient) {
				if (strlen($wlClient)>0) {
					$count++;
					$arrWlClients[$count] = explode("\t", $wlClient);
				}
			}

			if (!empty($arrWlClients)) {

				foreach ($arrWlClients as $index => $wlClient) {
					$oid = snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-signal-$interface\".". $index);

					$total_oids[] = $oid;

					$sensors[] = new WirelessSensor(
						'clients',
						$this->getDeviceId(),
						$oid,
						'arra-wlclient-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.' ['.$wlClient[0].']: ' . $wlClient[2]
					);
				}
			}

			$sensors[] = new WirelessSensor(
				'clients',
				$this->getDeviceId(),
				snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"clients-$interface\""),
				'arra-'.$interface,
				0,
				"Clients: $interfaceName",
				$count
			);

        }

        return $sensors;
    }

    /**
     * Discover wireless frequency.  This is in MHz. Type is frequency.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessFrequency(): array
    {
        return $this->getSensorData('frequency', '', False, False);
    }

    /**
     * Discover wireless noise floor.  This is in dBm. Type is noise-floor.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessNoiseFloor(): array
    {
        return $this->getSensorData('noise-floor', '', False, False);
    }

    /**
     * Discover wireless rate. This is in bps. Type is rate.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array
     * @throws Exception
     */
    public function discoverWirelessRate(): array
    {
        $txrate = $this->getSensorData('rate', '-tx', False, True);
        $rxrate = $this->getSensorData('rate', '-rx', False, True);
        return array_merge($txrate, $rxrate);
    }

    /**
     * Discover wireless snr. This is in dB. Type is snr.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array
     * @throws Exception
     */
    public function discoverWirelessSNR(): array
    {
	    return $this->getSensorData('snr', '', False, True);
    }

    /**
     * Discover wireless channel.  This is as number. Type is channel.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessChannel(): array
    {
        return $this->getSensorData('channel', '', False, False);
    }

    /**
     * Discover wireless power.  This is in dBm. Type is power.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessPower(): array
    {
        return $this->getSensorData('power', '', False, False);
    }

    /**
     * Discover wireless width.  This is in MHz. Type is width.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessWidth(): array
    {
        return $this->getSensorData('width', '', False, False);
    }

    /**
     * Discover wireless NodeInfo.  This is boolean. Type is NodeInfo.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessNodeinfo(): array
    {
		$sensors = [];

		$sensors[] = new WirelessSensor(
			'nodeinfo',
			$this->getDeviceId(),
			snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"nodeinfo-gateway\""),
			'nodeinfo-gateway',
			0,
			"Is Gateway (1 if is; 0 if not;)"
		);

		$nextNodeSNMPData = explode(PHP_EOL, snmp_get($this->getDevice()->toArray(), "NET-SNMP-EXTEND-MIB::nsExtendOutputFull.\"nodeinfo-next\"", '-Osqnv'));

		list($nextNodeName, $nextNodeInterface) = explode("\t", $nextNodeSNMPData[0]);


		if (!empty($nextNodeName) && !empty($nextNodeInterface)) {


			$sensors[] = new WirelessSensor(
				'nodeinfo',
				$this->getDeviceId(),
				snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutput1Line.\"nodeinfo-next\""),
				'nextnodeinfo',
				'nextnode-'.$nextNodeInterface.'-'.$nextNodeName,
				"Next node: $nextNodeName ($nextNodeInterface)"
			);
		}


        return $sensors;
    }

    /**
     * Discover wireless client rate. Type is clients rate.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     * @throws Exception
     */
    public function discoverWirelessClientsRate(): array
    {

		$interfaces = $this->getInterfaces();

		$sensors = array();

		foreach ($interfaces as $interface => $interfaceName) {

			$wlClients = $this->getWlClients($interface);
			$arrWlClients = array();
			$count = 0;
			foreach ($wlClients as $wlClient) {
				if (strlen($wlClient)>0) {
					$count++;
					$arrWlClients[$count] = explode("\t", $wlClient);
				}
			}

			if (!empty($arrWlClients)) {

				foreach ($arrWlClients as $index => $wlClient) {

					$sensors[] = new WirelessSensor(
						'clients-rate',
						$this->getDeviceId(),
						snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-rate-rx-$interface\".". $index),
						'arra-wlclientrate-rx-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.'-rx ['.$wlClient[0].']: ' . $wlClient[2]
					);

					$sensors[] = new WirelessSensor(
						'clients-rate',
						$this->getDeviceId(),
						snmp_translate("NET-SNMP-EXTEND-MIB::nsExtendOutLine.\"clients-rate-tx-$interface\".". $index),
						'arra-wlclientrate-tx-'.$interface,
						$interface.$wlClient[0], //$index,
						$interfaceName.'-tx ['.$wlClient[0].']: ' . $wlClient[2]
					);
				}
			}


        }

        return $sensors;
    }

    /**
     * Get wireless clients for a given interface
     *
     * @param string $interface
     * @return array
     * @throws Exception
     */
    private function getWlClients(string $interface): array
    {
        return explode(PHP_EOL, snmp_get($this->getDevice()->toArray(), "NET-SNMP-EXTEND-MIB::nsExtendOutputFull.\"clients-list-$interface\"", '-Osqnv'));
    }
}
